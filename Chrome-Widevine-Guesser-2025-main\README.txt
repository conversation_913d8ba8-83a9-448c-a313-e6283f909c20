Note:This is free version valid for 25 hits per ip..

1. Extract the zip file
2. Go to the extracted folder
3. You will see a license.json file, open it in any text editor.
4. update host and key as required. Make sure to save the file after editing!
5. Contact on telegram https://t.me/chromeapisupport to buy.
5 Chrome decrypt updates channel :  https://t.me/chromedecrypt


You can use the extension only on windows. You can use the extension in chrome/brave browsers. 
Note that the extension currently only works up to chrome browser version 112. If your browser is updated to a newer version, consider replacing the WidevineCdm folder of the chrome browser (usually can be found in a path like "C:\Program Files\Google\Chrome\Application\113.0.5672.64"), to the WidevineCdm folder of the version of 4.10.2557.0 (this folder be found inside this extension folder. Just copy the folder and replace it in the correct location.) Alternatively you can also try to downgrade your chrome browser to any version which is equal or lower than to 112.
The extension currently works on even on the latest version of brave browser. So if you have difficulties in downgrading chrome brwoser or in replacing the WidevineCDM folder, try using the brave browser and adding the extension in there.

Method of adding the extension in chrome browser:-
• Go to chrome > three dots at the top right corner > more tools > extensions
• Make sure to turn on the developer mode at the top right corner.
• Click "Load unpacked"
• Select the extracted folder. (Make sure to double click and go inside to the extracted folder and then press "Select Folder")

Method of adding the extension in brave browser:-
• Go to brave > three lines at the top right corner > extensions
• Make sure to turn on the developer mode at the top right corner.
• Click "Load unpacked"
• Select the extracted folder. (Make sure to double click and go inside to the extracted folder and then press "Select Folder")

5. Pin the extension.

Important :- Please disable any eme logger extension/scripts and any pssh capture extensions/scripts if you have added before.

6. Go to your DRM video an play it. (You may need to accept the install widevine popup when first playing a DRM video on brave browser and then reload the page again!)